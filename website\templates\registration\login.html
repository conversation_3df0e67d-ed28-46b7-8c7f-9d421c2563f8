{% extends 'website/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}Login - Gurukul Setu{% endblock %}

{% block content %}
<section class="login-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center py-3">
                        <h3 class="mb-0">{% trans "Login to Your Account" %}</h3>
                    </div>
                    <div class="card-body p-4">
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <p><strong>{% trans "Error:" %}</strong> {% trans "Please enter a correct username and password. Note that both fields may be case-sensitive." %}</p>
                            </div>
                        {% endif %}
                        
                        {% if next %}
                            {% if user.is_authenticated %}
                                <div class="alert alert-warning">
                                    <p>{% trans "Your account doesn't have access to this page. To proceed, please login with an account that has access." %}</p>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <p>{% trans "Please login to see this page." %}</p>
                                </div>
                            {% endif %}
                        {% endif %}
                        
                        <form method="post" action="{% url 'login' %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="id_username" class="form-label">{% trans "Username" %}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" name="username" id="id_username" class="form-control" placeholder="{% trans 'Enter your username' %}" required autofocus>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="id_password" class="form-label">{% trans "Password" %}</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" name="password" id="id_password" class="form-control" placeholder="{% trans 'Enter your password' %}" required>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember-me">
                                <label class="form-check-label" for="remember-me">{% trans "Remember me" %}</label>
                            </div>
                            
                            <input type="hidden" name="next" value="{{ next }}">
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i> {% trans "Login" %}
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <p>{% trans "Forgot your password?" %} <a href="{% url 'password_reset' %}">{% trans "Reset it here" %}</a></p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="{% url 'landing_page' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> {% trans "Back to Home" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
