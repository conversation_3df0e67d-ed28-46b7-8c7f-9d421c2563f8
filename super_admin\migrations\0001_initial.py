# Generated by Django 5.2 on 2025-05-22 17:37

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="College",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=200, verbose_name="College Name")),
                (
                    "code",
                    models.Char<PERSON>ield(
                        max_length=20, unique=True, verbose_name="College Code"
                    ),
                ),
                ("address", models.TextField(verbose_name="Address")),
                ("city", models.CharField(max_length=100, verbose_name="City")),
                ("state", models.Char<PERSON>ield(max_length=100, verbose_name="State")),
                ("pincode", models.Char<PERSON>ield(max_length=10, verbose_name="PIN Code")),
                ("email", models.EmailField(max_length=100, verbose_name="Email")),
                ("phone", models.<PERSON>r<PERSON><PERSON>(max_length=20, verbose_name="Phone")),
                (
                    "website",
                    models.URLField(blank=True, null=True, verbose_name="Website"),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="college_logos/",
                        verbose_name="Logo",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "subscription_start_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Subscription Start Date"
                    ),
                ),
                (
                    "subscription_end_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Subscription End Date"
                    ),
                ),
                (
                    "subscription_plan",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="Subscription Plan",
                    ),
                ),
                (
                    "admin_username",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="Admin Username",
                    ),
                ),
                (
                    "admin_email",
                    models.EmailField(
                        blank=True,
                        max_length=100,
                        null=True,
                        verbose_name="Admin Email",
                    ),
                ),
                ("is_active", models.BooleanField(default=True, verbose_name="Active")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
            ],
            options={
                "verbose_name": "College",
                "verbose_name_plural": "Colleges",
                "ordering": ["-created_at"],
            },
        ),
    ]
