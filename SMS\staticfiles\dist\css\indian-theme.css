/* Indian Theme CSS - Professional Edition for Education Sector
   Colors inspired by Indian flag and cultural elements:
   - Deep Saffron: #FF9933
   - White: #FFFFFF
   - India Green: #138808
   - Navy Blue (Ashoka Chakra): #000080
   - Accent colors from Indian art and textiles
*/

:root {
  --saffron: #FF9933;
  --india-green: #138808;
  --navy-blue: #000080;
  --chakra-blue: #0000CD;
  --light-saffron: #FFB366;
  --light-green: #5CB85C;
  --peacock-blue: #0D6EFD;
  --maroon: #800000;
  --sandstone: #F4F1EA;
  --clay: #D9BDA3;
  --spice-red: #A52A2A;
  --turmeric: #E3A857;
  --mehendi-green: #568203;
  --royal-blue: #4169E1;
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(90deg, var(--navy-blue), var(--chakra-blue)) !important;
  color: white !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.navbar .nav-link {
  color: white !important;
  transition: color 0.3s ease, transform 0.3s ease;
}

.navbar .nav-link:hover {
  color: var(--saffron) !important;
  text-decoration: none;
}

/* Sidebar Styles */
.sidebar, .main-sidebar {
  background: linear-gradient(90deg, var(--navy-blue), var(--chakra-blue)) !important;
  color: white !important;
}

/* Sidebar brand header */
.main-sidebar .brand-link {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Nav link styling */
.sidebar .nav-link:hover,
.nav-sidebar .nav-link:hover {
  color: var(--saffron) !important;
  background-color: rgba(0, 0, 128, 0.5) !important;
}

.sidebar .nav-link.active,
.nav-sidebar .nav-link.active {
  background-color: rgba(0, 0, 128, 0.7) !important;
  color: var(--saffron) !important;
  font-weight: bold !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Footer Styles */
.main-footer {
  background: linear-gradient(90deg, var(--navy-blue), var(--chakra-blue)) !important;
  color: white !important;
}

.footer i {
  color: var(--saffron) !important;
}

/* Brand Link Styles */
.brand-link {
  background: linear-gradient(90deg, var(--navy-blue), var(--chakra-blue));
  color: white !important;
}

.brand-link:hover {
  color: var(--saffron) !important;
  background: linear-gradient(90deg, #000066, var(--navy-blue));
}

/* Card Styles */
.card-header {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
  color: white !important;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
  border: none !important;
  color: #fff !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #000066, var(--navy-blue)) !important;
}

.btn-success {
  background: linear-gradient(135deg, var(--india-green), var(--mehendi-green)) !important;
  border: none !important;
}

/* Dashboard Card Styles */
.dashboard-card-icon.bg-primary {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
}

.dashboard-card-icon.bg-success {
  background: linear-gradient(135deg, var(--india-green), var(--mehendi-green)) !important;
}

.dashboard-card-icon.bg-warning {
  background: linear-gradient(135deg, var(--saffron), var(--turmeric)) !important;
}

.dashboard-card-icon.bg-danger {
  background: linear-gradient(135deg, var(--spice-red), var(--maroon)) !important;
}

/* Page Title Icon */
.page-title-icon {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
}

/* Badge Styles */
.badge.bg-primary {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, var(--india-green), var(--mehendi-green)) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, var(--saffron), var(--turmeric)) !important;
}

/* Loader Styles */
.school-logo {
  background: linear-gradient(135deg, var(--navy-blue), var(--chakra-blue)) !important;
}

.school-logo i {
  color: var(--saffron) !important;
}

/* Form Focus Styles */
.form-control:focus, .form-select:focus {
  border-color: var(--chakra-blue);
  box-shadow: 0 0 0 0.25rem rgba(0, 0, 205, 0.15);
}

/* Indian Pattern Background */
.indian-pattern-bg {
  background-color: var(--sandstone);
  background-image: url('../img/indian-pattern.png');
  background-repeat: repeat;
  background-size: 200px;
  position: relative;
}

.indian-pattern-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  z-index: 0;
}

/* Ashoka Chakra Inspired Spinner */
.chakra-spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 4px solid rgba(0, 0, 205, 0.1);
  border-top: 4px solid var(--chakra-blue);
  animation: chakra-spin 1s linear infinite;
}

@keyframes chakra-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Indian Festival Colors for Highlights */
.festival-highlight {
  background: linear-gradient(135deg, rgba(255, 153, 51, 0.15), rgba(19, 136, 8, 0.15));
  border-left: 4px solid var(--saffron);
  padding: 15px;
  border-radius: 5px;
}

/* UDISE+ Specific Styling */
.udise-header {
  background-color: var(--navy-blue);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.udise-logo {
  height: 40px;
  margin-right: 15px;
}

/* Indian Education Board Badges */
.board-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
  font-size: 12px;
  margin-right: 5px;
}

.board-badge.cbse {
  background-color: var(--chakra-blue);
  color: white;
}

.board-badge.icse {
  background-color: var(--maroon);
  color: white;
}

.board-badge.state {
  background-color: var(--india-green);
  color: white;
}

/* RTE Quota Indicator */
.rte-indicator {
  background-color: var(--saffron);
  color: white;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

/* Indian Currency Symbol */
.rupee-symbol {
  font-family: Arial, sans-serif;
  font-weight: bold;
}

.rupee-symbol::before {
  content: '₹';
  margin-right: 2px;
}
