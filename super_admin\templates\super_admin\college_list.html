{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}Colleges - Super Admin | Gurukul Setu{% endblock %}

{% block page_title %}Manage Colleges{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/college-management.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" name="q" class="form-control" placeholder="Search colleges..." value="{{ request.GET.q|default:'' }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select" onchange="this.form.submit()">
                        <option value="">All Colleges</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active Only</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive Only</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{% url 'super_admin:college_create' %}" class="btn btn-success w-100">
                        <i class="fas fa-plus"></i> Add College
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Colleges List -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-university me-2"></i> Colleges
        </div>
        <div class="card-body">
            {% if colleges %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Code</th>
                                <th>Location</th>
                                <th>Contact</th>
                                <th>Subscription</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for college in colleges %}
                                <tr>
                                    <td>
                                        {% if college.logo %}
                                            <img src="{{ college.logo.url }}" alt="{{ college.name }}" width="30" class="me-2">
                                        {% endif %}
                                        {{ college.name }}
                                    </td>
                                    <td>{{ college.code }}</td>
                                    <td>{{ college.city }}, {{ college.state }}</td>
                                    <td>
                                        <small>
                                            <i class="fas fa-envelope me-1"></i> {{ college.email }}<br>
                                            <i class="fas fa-phone me-1"></i> {{ college.phone }}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Plan:</strong> {{ college.subscription_plan|default:"N/A" }}<br>
                                            <strong>Expires:</strong> {{ college.subscription_end_date|default:"N/A" }}
                                        </small>
                                    </td>
                                    <td>
                                        {% if college.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-sm btn-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'super_admin:college_update' college.pk %}" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'super_admin:toggle_college_status' college.pk %}" class="btn btn-sm {% if college.is_active %}btn-danger{% else %}btn-success{% endif %}" title="{% if college.is_active %}Deactivate{% else %}Activate{% endif %}">
                                                <i class="fas {% if college.is_active %}fa-times{% else %}fa-check{% endif %}"></i>
                                            </a>
                                            <a href="{% url 'super_admin:college_delete' college.pk %}" class="btn btn-sm btn-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-university fa-4x mb-3 text-muted"></i>
                    <h4>No Colleges Found</h4>
                    <p>There are no colleges matching your search criteria.</p>
                    <a href="{% url 'super_admin:college_create' %}" class="btn btn-primary mt-3">
                        <i class="fas fa-plus me-2"></i> Add New College
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
