{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}Dashboard - Super Admin | Gurukul Setu{% endblock %}

{% block page_title %}Super Admin Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-university fa-3x mb-3 text-primary"></i>
                    <h5 class="card-title">Total Colleges</h5>
                    <h2 class="mb-0">{{ total_colleges }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <h5 class="card-title">Active Colleges</h5>
                    <h2 class="mb-0">{{ active_colleges }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-3x mb-3 text-danger"></i>
                    <h5 class="card-title">Inactive Colleges</h5>
                    <h2 class="mb-0">{{ inactive_colleges }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Expiring Subscriptions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-calendar-times me-2"></i> Expiring Subscriptions
                </div>
                <div class="card-body">
                    {% if expiring_subscriptions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>College</th>
                                        <th>Plan</th>
                                        <th>Expiry Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for college in expiring_subscriptions %}
                                        <tr>
                                            <td>{{ college.name }}</td>
                                            <td>{{ college.subscription_plan }}</td>
                                            <td>{{ college.subscription_end_date }}</td>
                                            <td>
                                                <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center">No subscriptions expiring soon.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Colleges -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-university me-2"></i> Recently Added Colleges
                </div>
                <div class="card-body">
                    {% if recent_colleges %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>College</th>
                                        <th>Status</th>
                                        <th>Added On</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for college in recent_colleges %}
                                        <tr>
                                            <td>{{ college.name }}</td>
                                            <td>
                                                {% if college.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ college.created_at|date:"d M Y" }}</td>
                                            <td>
                                                <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center">No colleges added recently.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i> Quick Actions
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:college_create' %}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i> Add New College
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:college_list' %}" class="btn btn-info w-100">
                                <i class="fas fa-list me-2"></i> View All Colleges
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:college_report' %}" class="btn btn-success w-100">
                                <i class="fas fa-chart-bar me-2"></i> College Reports
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:subscription_report' %}" class="btn btn-warning w-100">
                                <i class="fas fa-calendar-check me-2"></i> Subscription Reports
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:subscription_plan_list' %}" class="btn btn-info w-100">
                                <i class="fas fa-tags me-2"></i> Subscription Plans
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'super_admin:subscription_history' %}" class="btn btn-primary w-100">
                                <i class="fas fa-history me-2"></i> Subscription History
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
