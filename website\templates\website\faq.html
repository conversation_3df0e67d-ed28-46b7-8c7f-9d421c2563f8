{% extends 'website/base.html' %}
{% load static %}

{% block title %}Frequently Asked Questions - Gurukul Setu{% endblock %}

{% block content %}
<!-- Page Banner -->
<section class="page-banner">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="page-banner-content">
                    <h1>Frequently Asked Questions</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'landing_page' %}">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">FAQ</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Search Section -->
<section class="faq-search-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="faq-search-content text-center">
                    <h2>How Can We Help You?</h2>
                    <p>Find answers to frequently asked questions about Gurukul Setu. If you can't find what you're looking for, please contact our support team.</p>
                    <div class="faq-search-form">
                        <form id="faqSearchForm">
                            <div class="input-group">
                                <input type="text" class="form-control" id="faqSearch" placeholder="Search for questions...">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i> Search</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Categories Section -->
<section class="faq-categories-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="faq-category-card" data-category="general">
                    <div class="icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <h3>General</h3>
                    <p>Basic information about Gurukul Setu</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="faq-category-card" data-category="pricing">
                    <div class="icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h3>Pricing</h3>
                    <p>Questions about plans and payments</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="faq-category-card" data-category="features">
                    <div class="icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>Features</h3>
                    <p>Details about system capabilities</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="faq-category-card" data-category="technical">
                    <div class="icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <h3>Technical</h3>
                    <p>Technical specifications and support</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Content Section -->
<section class="faq-content-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-3">
                <div class="faq-sidebar">
                    <h4>FAQ Categories</h4>
                    <ul class="faq-nav">
                        <li class="active"><a href="#general" data-category="general">General Questions</a></li>
                        <li><a href="#pricing" data-category="pricing">Pricing & Plans</a></li>
                        <li><a href="#features" data-category="features">Features & Functionality</a></li>
                        <li><a href="#technical" data-category="technical">Technical Support</a></li>
                        <li><a href="#implementation" data-category="implementation">Implementation</a></li>
                        <li><a href="#security" data-category="security">Security & Privacy</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-9">
                <!-- General Questions -->
                <div class="faq-category-content active" id="general">
                    <h3>General Questions</h3>
                    <div class="accordion" id="accordionGeneral">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingG1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseG1" aria-expanded="true" aria-controls="collapseG1">
                                    What is Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseG1" class="accordion-collapse collapse show" aria-labelledby="headingG1" data-bs-parent="#accordionGeneral">
                                <div class="accordion-body">
                                    Gurukul Setu is a comprehensive School and College Management System designed specifically for Indian educational institutions. Our platform helps streamline administrative processes, enhance teaching methodologies, and improve overall student outcomes through a suite of integrated modules including student management, staff management, fee management, examination system, attendance tracking, and document management.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingG2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseG2" aria-expanded="false" aria-controls="collapseG2">
                                    Who can use Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseG2" class="accordion-collapse collapse" aria-labelledby="headingG2" data-bs-parent="#accordionGeneral">
                                <div class="accordion-body">
                                    Gurukul Setu is designed for all types of educational institutions in India, including primary schools, secondary schools, K-12 schools, colleges, universities, and educational groups with multiple institutions. Our system is scalable and can be customized to meet the specific needs of institutions of all sizes.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingG3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseG3" aria-expanded="false" aria-controls="collapseG3">
                                    How long has Gurukul Setu been in operation?
                                </button>
                            </h2>
                            <div id="collapseG3" class="accordion-collapse collapse" aria-labelledby="headingG3" data-bs-parent="#accordionGeneral">
                                <div class="accordion-body">
                                    Gurukul Setu was founded in 2018 and has been serving educational institutions across India for over 5 years. During this time, we have continuously evolved our platform based on user feedback and industry best practices to provide the most comprehensive and user-friendly school management system available.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingG4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseG4" aria-expanded="false" aria-controls="collapseG4">
                                    How many institutions are currently using Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseG4" class="accordion-collapse collapse" aria-labelledby="headingG4" data-bs-parent="#accordionGeneral">
                                <div class="accordion-body">
                                    Currently, over 500 educational institutions across India are using Gurukul Setu to manage their operations. Our client base includes schools and colleges of all sizes, from small primary schools to large university campuses, serving a combined student population of more than 100,000.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingG5">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseG5" aria-expanded="false" aria-controls="collapseG5">
                                    What makes Gurukul Setu different from other school management systems?
                                </button>
                            </h2>
                            <div id="collapseG5" class="accordion-collapse collapse" aria-labelledby="headingG5" data-bs-parent="#accordionGeneral">
                                <div class="accordion-body">
                                    Gurukul Setu stands out for its comprehensive approach, user-friendly interface, and specific customization for Indian educational institutions. Our system is designed with an understanding of the unique requirements of Indian schools and colleges, including compliance with local regulations and educational frameworks. Additionally, our dedicated customer support, regular updates, and commitment to data security make us the preferred choice for educational institutions across the country.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pricing & Plans -->
                <div class="faq-category-content" id="pricing">
                    <h3>Pricing & Plans</h3>
                    <div class="accordion" id="accordionPricing">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingP1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseP1" aria-expanded="true" aria-controls="collapseP1">
                                    How much does Gurukul Setu cost?
                                </button>
                            </h2>
                            <div id="collapseP1" class="accordion-collapse collapse show" aria-labelledby="headingP1" data-bs-parent="#accordionPricing">
                                <div class="accordion-body">
                                    Gurukul Setu offers flexible pricing plans based on the size of your institution and the features you need. Our Basic plan starts at ₹4,999 per month, the Standard plan at ₹9,999 per month, and the Premium plan at ₹19,999 per month. We also offer annual billing with a 20% discount. For detailed pricing information, please visit our <a href="{% url 'pricing' %}">Pricing page</a>.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingP2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseP2" aria-expanded="false" aria-controls="collapseP2">
                                    Do you offer any discounts for educational institutions?
                                </button>
                            </h2>
                            <div id="collapseP2" class="accordion-collapse collapse" aria-labelledby="headingP2" data-bs-parent="#accordionPricing">
                                <div class="accordion-body">
                                    Yes, we offer special discounts for government schools, non-profit educational institutions, and schools in rural areas. We also provide discounted rates for educational groups with multiple institutions. Please contact our sales team to learn more about our discount programs and to get a customized quote for your specific situation.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingP3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseP3" aria-expanded="false" aria-controls="collapseP3">
                                    What payment methods do you accept?
                                </button>
                            </h2>
                            <div id="collapseP3" class="accordion-collapse collapse" aria-labelledby="headingP3" data-bs-parent="#accordionPricing">
                                <div class="accordion-body">
                                    We accept all major credit cards, debit cards, net banking, UPI, and bank transfers. For annual plans, we can also provide invoices for direct bank transfers. Our payment system is secure and compliant with industry standards to ensure the safety of your financial information.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingP4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseP4" aria-expanded="false" aria-controls="collapseP4">
                                    Is there a free trial available?
                                </button>
                            </h2>
                            <div id="collapseP4" class="accordion-collapse collapse" aria-labelledby="headingP4" data-bs-parent="#accordionPricing">
                                <div class="accordion-body">
                                    Yes, we offer a 14-day free trial for all our plans. No credit card is required to start your trial. You can explore all the features and decide which plan is right for your institution. To start your free trial, simply <a href="{% url 'demo' %}">request a demo</a> and our team will set up a trial account for you.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingP5">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseP5" aria-expanded="false" aria-controls="collapseP5">
                                    Can I upgrade or downgrade my plan later?
                                </button>
                            </h2>
                            <div id="collapseP5" class="accordion-collapse collapse" aria-labelledby="headingP5" data-bs-parent="#accordionPricing">
                                <div class="accordion-body">
                                    Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll be charged the prorated difference for the remainder of your billing cycle. When downgrading, the new rate will apply at the start of your next billing cycle. All your data will be preserved when changing plans.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Features & Functionality -->
                <div class="faq-category-content" id="features">
                    <h3>Features & Functionality</h3>
                    <div class="accordion" id="accordionFeatures">
                        <!-- More accordion items for features category -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingF1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseF1" aria-expanded="true" aria-controls="collapseF1">
                                    What modules are included in Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseF1" class="accordion-collapse collapse show" aria-labelledby="headingF1" data-bs-parent="#accordionFeatures">
                                <div class="accordion-body">
                                    Gurukul Setu includes several integrated modules: Student Management, Staff Management, Fee Management, Examination System, Attendance Tracking, and Document Management. Depending on your plan, you may also have access to advanced reporting and analytics, API access, and custom branding options. For a detailed breakdown of features by plan, please visit our <a href="{% url 'features' %}">Features page</a>.
                                </div>
                            </div>
                        </div>
                        <!-- Additional feature FAQs would be added here -->
                    </div>
                </div>
                
                <!-- Technical Support -->
                <div class="faq-category-content" id="technical">
                    <h3>Technical Support</h3>
                    <div class="accordion" id="accordionTechnical">
                        <!-- More accordion items for technical category -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingT1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseT1" aria-expanded="true" aria-controls="collapseT1">
                                    What kind of technical support do you offer?
                                </button>
                            </h2>
                            <div id="collapseT1" class="accordion-collapse collapse show" aria-labelledby="headingT1" data-bs-parent="#accordionTechnical">
                                <div class="accordion-body">
                                    Our support varies by plan. Basic plan users receive email support, Standard plan users get email and chat support, and Premium plan users enjoy priority email, chat, and phone support. All users have access to our comprehensive knowledge base, video tutorials, and regular webinars. Our support team is available during business hours (9 AM to 6 PM IST, Monday to Saturday).
                                </div>
                            </div>
                        </div>
                        <!-- Additional technical FAQs would be added here -->
                    </div>
                </div>
                
                <!-- Implementation -->
                <div class="faq-category-content" id="implementation">
                    <h3>Implementation</h3>
                    <div class="accordion" id="accordionImplementation">
                        <!-- More accordion items for implementation category -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingI1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseI1" aria-expanded="true" aria-controls="collapseI1">
                                    How long does it take to implement Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseI1" class="accordion-collapse collapse show" aria-labelledby="headingI1" data-bs-parent="#accordionImplementation">
                                <div class="accordion-body">
                                    The implementation timeline varies depending on the size of your institution and the complexity of your requirements. Typically, basic implementation can be completed within 1-2 weeks, while more comprehensive setups may take 3-4 weeks. Our implementation team works closely with you to ensure a smooth transition and minimal disruption to your operations.
                                </div>
                            </div>
                        </div>
                        <!-- Additional implementation FAQs would be added here -->
                    </div>
                </div>
                
                <!-- Security & Privacy -->
                <div class="faq-category-content" id="security">
                    <h3>Security & Privacy</h3>
                    <div class="accordion" id="accordionSecurity">
                        <!-- More accordion items for security category -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingS1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseS1" aria-expanded="true" aria-controls="collapseS1">
                                    How secure is my data with Gurukul Setu?
                                </button>
                            </h2>
                            <div id="collapseS1" class="accordion-collapse collapse show" aria-labelledby="headingS1" data-bs-parent="#accordionSecurity">
                                <div class="accordion-body">
                                    We take data security very seriously. Gurukul Setu employs industry-standard encryption, regular security audits, and strict access controls to ensure your data remains safe and confidential. Our servers are hosted in secure data centers with multiple layers of physical and digital security. We are compliant with relevant data protection regulations and follow best practices for data security and privacy.
                                </div>
                            </div>
                        </div>
                        <!-- Additional security FAQs would be added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Still Have Questions Section -->
<section class="still-questions-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="still-questions-content text-center">
                    <h2>Still Have Questions?</h2>
                    <p>If you couldn't find the answer to your question, please don't hesitate to contact our support team. We're here to help!</p>
                    <div class="contact-options">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="contact-option">
                                    <div class="icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <h4>Email Us</h4>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="contact-option">
                                    <div class="icon">
                                        <i class="fas fa-phone-alt"></i>
                                    </div>
                                    <h4>Call Us</h4>
                                    <p>+91 98765 43210</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="contact-option">
                                    <div class="icon">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h4>Live Chat</h4>
                                    <p>Available 9 AM - 6 PM</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url 'contact' %}" class="btn btn-primary mt-4">Contact Us</a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ Search Functionality
        const faqSearchForm = document.getElementById('faqSearchForm');
        const faqSearch = document.getElementById('faqSearch');
        const accordionItems = document.querySelectorAll('.accordion-item');
        
        if (faqSearchForm) {
            faqSearchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const searchTerm = faqSearch.value.toLowerCase().trim();
                
                if (searchTerm) {
                    // Hide all accordion items first
                    accordionItems.forEach(item => {
                        item.style.display = 'none';
                    });
                    
                    // Show items that match the search term
                    accordionItems.forEach(item => {
                        const questionText = item.querySelector('.accordion-button').textContent.toLowerCase();
                        const answerText = item.querySelector('.accordion-body').textContent.toLowerCase();
                        
                        if (questionText.includes(searchTerm) || answerText.includes(searchTerm)) {
                            item.style.display = 'block';
                            
                            // Expand the matching item
                            const collapseElement = item.querySelector('.accordion-collapse');
                            const button = item.querySelector('.accordion-button');
                            
                            if (collapseElement.classList.contains('collapse')) {
                                collapseElement.classList.add('show');
                                button.classList.remove('collapsed');
                                button.setAttribute('aria-expanded', 'true');
                            }
                        }
                    });
                } else {
                    // If search is empty, show all items
                    accordionItems.forEach(item => {
                        item.style.display = 'block';
                    });
                }
            });
        }
        
        // FAQ Category Navigation
        const faqNavLinks = document.querySelectorAll('.faq-nav a');
        const faqCategoryContents = document.querySelectorAll('.faq-category-content');
        const faqCategoryCards = document.querySelectorAll('.faq-category-card');
        
        function showCategory(categoryId) {
            // Hide all category contents
            faqCategoryContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Show selected category content
            const selectedCategory = document.getElementById(categoryId);
            if (selectedCategory) {
                selectedCategory.classList.add('active');
            }
            
            // Update active state in navigation
            faqNavLinks.forEach(link => {
                link.parentElement.classList.remove('active');
                
                if (link.getAttribute('data-category') === categoryId) {
                    link.parentElement.classList.add('active');
                }
            });
        }
        
        // Add click event to navigation links
        faqNavLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const categoryId = this.getAttribute('data-category');
                showCategory(categoryId);
            });
        });
        
        // Add click event to category cards
        faqCategoryCards.forEach(card => {
            card.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-category');
                showCategory(categoryId);
                
                // Scroll to the content section
                document.querySelector('.faq-content-section').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    });
</script>
{% endblock %}
