{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}Subscription Plans | Gurukul Setu{% endblock %}

{% block page_title %}Subscription Plans{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/subscription-management.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-tags me-2"></i> Subscription Plans
                    </div>
                    <a href="{% url 'super_admin:subscription_plan_create' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i> Add New Plan
                    </a>
                </div>
                <div class="card-body">
                    {% if plans %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="plansTable">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Duration</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for plan in plans %}
                                        <tr>
                                            <td>{{ plan.name }}</td>
                                            <td>{{ plan.duration_months }} months</td>
                                            <td>₹{{ plan.price }}</td>
                                            <td>
                                                {% if plan.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'super_admin:subscription_plan_update' plan.pk %}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'super_admin:subscription_plan_delete' plan.pk %}" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No subscription plans found.
                            <a href="{% url 'super_admin:subscription_plan_create' %}">Create your first plan</a>.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#plansTable').DataTable({
            order: [[2, 'asc']]
        });
    });
</script>
{% endblock %}
