{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Super Admin - Gurukul Setu{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/super-admin.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="{% static 'img/Gurukul_Setu.png' %}" alt="Gurukul Setu Logo">
            <h3>Super Admin</h3>
        </div>
        <div class="sidebar-menu">
            <ul>
                <li>
                    <a href="{% url 'super_admin:dashboard' %}" class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li>
                    <a href="{% url 'super_admin:college_list' %}" class="{% if 'college' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-university"></i> Colleges
                    </a>
                </li>
                <li>
                    <a href="{% url 'super_admin:college_report' %}" class="{% if request.resolver_match.url_name == 'college_report' %}active{% endif %}">
                        <i class="fas fa-chart-bar"></i> College Reports
                    </a>
                </li>
                <li>
                    <a href="{% url 'super_admin:subscription_report' %}" class="{% if request.resolver_match.url_name == 'subscription_report' %}active{% endif %}">
                        <i class="fas fa-calendar-check"></i> Subscription Reports
                    </a>
                </li>

                <li>
                    <a href="{% url 'landing_page' %}">
                        <i class="fas fa-globe"></i> Website
                    </a>
                </li>
                <li>
                    <a href="{% url 'logout' %}">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Topbar -->
        <div class="topbar">
            <h1>{% block page_title %}Super Admin Dashboard{% endblock %}</h1>
            <div class="user-info">
                <img src="{% static 'img/avatar.png' %}" alt="User Avatar">
                <span>{{ request.user.username }}</span>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Content -->
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile sidebar toggle
            const toggleSidebar = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleSidebar) {
                toggleSidebar.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
