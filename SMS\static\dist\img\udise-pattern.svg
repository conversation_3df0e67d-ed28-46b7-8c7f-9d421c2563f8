<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
  <defs>
    <pattern id="udisePattern" patternUnits="userSpaceOnUse" width="100" height="100">
      <rect width="100" height="100" fill="#f8f9fa"/>
      
      <!-- Ashoka Chakra inspired elements -->
      <circle cx="50" cy="50" r="3" fill="#0e4686" opacity="0.1"/>
      <circle cx="50" cy="50" r="8" stroke="#0e4686" stroke-width="0.5" fill="none" opacity="0.1"/>
      <circle cx="50" cy="50" r="15" stroke="#0e4686" stroke-width="0.5" fill="none" opacity="0.1"/>
      
      <!-- Spokes -->
      <g opacity="0.1">
        <line x1="50" y1="35" x2="50" y2="30" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="50" y1="65" x2="50" y2="70" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="35" y1="50" x2="30" y2="50" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="65" y1="50" x2="70" y2="50" stroke="#0e4686" stroke-width="0.5"/>
        
        <line x1="38.8" y1="38.8" x2="35.4" y2="35.4" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="61.2" y1="61.2" x2="64.6" y2="64.6" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="38.8" y1="61.2" x2="35.4" y2="64.6" stroke="#0e4686" stroke-width="0.5"/>
        <line x1="61.2" y1="38.8" x2="64.6" y2="35.4" stroke="#0e4686" stroke-width="0.5"/>
      </g>
      
      <!-- Grid elements -->
      <rect x="0" y="0" width="100" height="1" fill="#0e4686" opacity="0.03"/>
      <rect x="0" y="0" width="1" height="100" fill="#0e4686" opacity="0.03"/>
      <rect x="0" y="99" width="100" height="1" fill="#0e4686" opacity="0.03"/>
      <rect x="99" y="0" width="1" height="100" fill="#0e4686" opacity="0.03"/>
      
      <!-- Decorative elements -->
      <circle cx="0" cy="0" r="2" fill="#0e4686" opacity="0.05"/>
      <circle cx="100" cy="0" r="2" fill="#0e4686" opacity="0.05"/>
      <circle cx="0" cy="100" r="2" fill="#0e4686" opacity="0.05"/>
      <circle cx="100" cy="100" r="2" fill="#0e4686" opacity="0.05"/>
    </pattern>
  </defs>
  
  <rect width="100" height="100" fill="url(#udisePattern)"/>
</svg>
