{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:'en' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gurukul Setu - School & College Management System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <style>
        /* Super Admin Badge Styles */
        .super-admin-badge {
            background-color: #309898;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            margin-left: 10px;
        }

        .super-admin-badge:hover {
            background-color: #1a5555;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .super-admin-badge i {
            margin-right: 4px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header Section -->
    <header class="header-area">
        <div class="container">
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <a class="navbar-brand" href="{% url 'landing_page' %}">
                        <img src="{% static 'img/Gurukul_Setu.png' %}" alt="Gurukul Setu Logo" class="logo">
                        <span>Gurukul Setu</span>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link {% if request.resolver_match.url_name == 'landing_page' %}active{% endif %}" href="{% url 'landing_page' %}">Home</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.resolver_match.url_name == 'features' %}active{% endif %}" href="{% url 'features' %}">Features</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {% if request.resolver_match.url_name == 'pricing' %}active{% endif %}" href="{% url 'pricing' %}">Pricing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {% if request.resolver_match.url_name == 'blog' or request.resolver_match.url_name == 'blog_detail' or request.resolver_match.url_name == 'testimonials' %}active{% endif %}" href="#" id="resourcesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Resources
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="resourcesDropdown">
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'blog' or request.resolver_match.url_name == 'blog_detail' %}active{% endif %}" href="{% url 'blog' %}">Blog</a></li>
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'testimonials' %}active{% endif %}" href="{% url 'testimonials' %}">Testimonials</a></li>
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'faq' %}active{% endif %}" href="{% url 'faq' %}">FAQ</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {% if request.resolver_match.url_name == 'about' or request.resolver_match.url_name == 'contact' or request.resolver_match.url_name == 'careers' %}active{% endif %}" href="#" id="companyDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Company
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="companyDropdown">
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'about' %}active{% endif %}" href="{% url 'about' %}">About Us</a></li>
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'careers' %}active{% endif %}" href="{% url 'careers' %}">Careers</a></li>
                                    <li><a class="dropdown-item {% if request.resolver_match.url_name == 'contact' %}active{% endif %}" href="{% url 'contact' %}">Contact Us</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="btn btn-login" href="{% url 'login' %}">Login</a>
                            </li>
                            <li class="nav-item ms-2">
                                <a class="btn btn-outline-primary" href="{% url 'demo' %}">Request Demo</a>
                            </li>
                            <li class="nav-item ms-2">
                                <div class="language-switcher">
                                    <div class="dropdown">
                                        <button class="btn dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-globe"></i>
                                            {% if LANGUAGE_CODE == 'en' %}
                                                <span>EN</span>
                                            {% elif LANGUAGE_CODE == 'hi' %}
                                                <span>HI</span>
                                            {% elif LANGUAGE_CODE == 'ta' %}
                                                <span>TA</span>
                                            {% elif LANGUAGE_CODE == 'te' %}
                                                <span>TE</span>
                                            {% elif LANGUAGE_CODE == 'kn' %}
                                                <span>KN</span>
                                            {% else %}
                                                <span>EN</span>
                                            {% endif %}
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                                            <form action="{% url 'set_language' %}" method="post" id="language-form">
                                                {% csrf_token %}
                                                <input name="next" type="hidden" value="{{ request.path }}">

                                                <li>
                                                    <button type="submit" name="language" value="en" class="dropdown-item {% if LANGUAGE_CODE == 'en' %}active{% endif %}">
                                                        <i class="fas fa-language"></i> English
                                                    </button>
                                                </li>
                                                <li>
                                                    <button type="submit" name="language" value="hi" class="dropdown-item {% if LANGUAGE_CODE == 'hi' %}active{% endif %}">
                                                        <i class="fas fa-language"></i> हिन्दी
                                                    </button>
                                                </li>
                                                <li>
                                                    <button type="submit" name="language" value="ta" class="dropdown-item {% if LANGUAGE_CODE == 'ta' %}active{% endif %}">
                                                        <i class="fas fa-language"></i> தமிழ்
                                                    </button>
                                                </li>
                                                <li>
                                                    <button type="submit" name="language" value="te" class="dropdown-item {% if LANGUAGE_CODE == 'te' %}active{% endif %}">
                                                        <i class="fas fa-language"></i> తెలుగు
                                                    </button>
                                                </li>
                                                <li>
                                                    <button type="submit" name="language" value="kn" class="dropdown-item {% if LANGUAGE_CODE == 'kn' %}active{% endif %}">
                                                        <i class="fas fa-language"></i> ಕನ್ನಡ
                                                    </button>
                                                </li>
                                            </form>
                                        </ul>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer Section -->
    <footer class="footer-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="footer-widget">
                        <div class="footer-logo">
                            <img src="{% static 'img/Gurukul_Setu.png' %}" alt="Gurukul Setu Logo">
                            <span>Gurukul Setu</span>
                        </div>
                        <p>Empowering educational institutions with modern technology solutions for better administration and enhanced learning experiences.</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-widget">
                        <h4>Quick Links</h4>
                        <ul class="footer-links">
                            <li><a href="{% url 'landing_page' %}"><i class="fas fa-angle-right"></i> Home</a></li>
                            <li><a href="{% url 'features' %}"><i class="fas fa-angle-right"></i> Features</a></li>
                            <li><a href="{% url 'pricing' %}"><i class="fas fa-angle-right"></i> Pricing</a></li>
                            <li><a href="{% url 'blog' %}"><i class="fas fa-angle-right"></i> Blog</a></li>
                            <li><a href="{% url 'testimonials' %}"><i class="fas fa-angle-right"></i> Testimonials</a></li>
                            <li><a href="{% url 'faq' %}"><i class="fas fa-angle-right"></i> FAQ</a></li>
                            <li><a href="{% url 'about' %}"><i class="fas fa-angle-right"></i> About</a></li>
                            <li><a href="{% url 'careers' %}"><i class="fas fa-angle-right"></i> Careers</a></li>
                            <li><a href="{% url 'contact' %}"><i class="fas fa-angle-right"></i> Contact</a></li>
                            <li><a href="{% url 'demo' %}"><i class="fas fa-angle-right"></i> Request Demo</a></li>
                            <li><a href="{% url 'login' %}"><i class="fas fa-angle-right"></i> Login</a></li>
                            <li><a href="{% url 'sitemap' %}"><i class="fas fa-angle-right"></i> Sitemap</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h4>Our Services</h4>
                        <ul class="footer-links">
                            <li><a href="{% url 'features' %}#student-management"><i class="fas fa-angle-right"></i> Student Management</a></li>
                            <li><a href="{% url 'features' %}#staff-management"><i class="fas fa-angle-right"></i> Staff Management</a></li>
                            <li><a href="{% url 'features' %}#fee-management"><i class="fas fa-angle-right"></i> Fee Management</a></li>
                            <li><a href="{% url 'features' %}#examination-system"><i class="fas fa-angle-right"></i> Examination System</a></li>
                            <li><a href="{% url 'features' %}#document-management"><i class="fas fa-angle-right"></i> Document Management</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h4>Contact Info</h4>
                        <ul class="footer-links">
                            <li><a href="#"><i class="fas fa-map-marker-alt"></i> 123 Education Street, Tech Park, Bangalore - 560001, India</a></li>
                            <li><a href="tel:+************"><i class="fas fa-phone-alt"></i> +91 98765 43210</a></li>
                            <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a></li>
                        </ul>
                        <div class="newsletter-form">
                            <input type="email" placeholder="Your Email">
                            <button type="submit">Subscribe</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="copyright-area">
                <div class="row">
                    <div class="col-md-6 d-flex align-items-center">
                        <p class="mb-0">&copy; 2025 Gurukul Setu. All Rights Reserved.</p>
                        <a href="{% url 'super_admin:login' %}" class="super-admin-badge">
                            <i class="fas fa-user-shield"></i> Super Admin
                        </a>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-bottom-links">
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                            <li><a href="#">Cookie Policy</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top"><i class="fas fa-arrow-up"></i></a>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <!-- Chatbot Widget -->
    {% include 'website/includes/chatbot.html' %}

    <!-- Cookie Consent Banner -->
    {% include 'website/includes/cookie_consent.html' %}
</body>
</html>
