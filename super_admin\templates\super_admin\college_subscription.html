{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load widget_tweaks %}
{% load static %}

{% block title %}Manage Subscription - {{ college.name }} | Gurukul Setu{% endblock %}

{% block page_title %}Manage Subscription - {{ college.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/subscription-management.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-calendar-check me-2"></i> Manage College Subscription
                </div>
                <div class="card-body">
                    <!-- Current Subscription Details -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Current Subscription Details</h5>
                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>College:</strong> {{ college.name }}</p>
                                    <p><strong>Current Plan:</strong> {{ college.subscription_plan|default:"Not Set" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Start Date:</strong> {{ college.subscription_start_date|default:"Not Set" }}</p>
                                    <p><strong>End Date:</strong> {{ college.subscription_end_date|default:"Not Set" }}</p>
                                    <p>
                                        <strong>Status:</strong>
                                        {% if college.subscription_end_date %}
                                            {% if college.is_subscription_active %}
                                                {% if college.get_subscription_status == "Expiring Soon" %}
                                                    <span class="badge bg-warning">Expiring Soon</span>
                                                {% else %}
                                                    <span class="badge bg-success">Active</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-danger">Expired</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">Not Set</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Update Subscription Form -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Update Subscription</h5>
                            <hr>

                            <form method="post">
                                {% csrf_token %}

                                {% if form.errors %}
                                    <div class="alert alert-danger">
                                        <strong>Please correct the following errors:</strong>
                                        <ul>
                                            {% for field in form %}
                                                {% for error in field.errors %}
                                                    <li>{{ field.label }}: {{ error }}</li>
                                                {% endfor %}
                                            {% endfor %}
                                            {% for error in form.non_field_errors %}
                                                <li>{{ error }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                {% endif %}

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.subscription_plan.id_for_label }}" class="form-label">{{ form.subscription_plan.label }} *</label>
                                            {{ form.subscription_plan|add_class:"form-select" }}
                                            {% if form.subscription_plan.help_text %}
                                                <small class="form-text text-muted">{{ form.subscription_plan.help_text }}</small>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                                            {{ form.start_date|add_class:"form-control"|attr:"type:date" }}
                                            {% if form.start_date.help_text %}
                                                <small class="form-text text-muted">{{ form.start_date.help_text }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between">
                                            <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-2"></i> Back to College
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i> Update Subscription
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
