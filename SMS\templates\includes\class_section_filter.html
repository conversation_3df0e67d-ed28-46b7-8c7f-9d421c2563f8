{% load static %}

<div class="card shadow p-3 mb-4">
    <h5 class="mb-3">🔍 Filter</h5>
    <form method="GET" class="row g-3 align-items-end" id="filter-form">
        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
            <label class="form-label fw-bold">Select Class:</label>
            {{ filter_form.class_name }}
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
            <label class="form-label fw-bold">Select Section:</label>
            {{ filter_form.section }}
        </div>
        {% if show_search %}
        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
            <label class="form-label fw-bold">Search</label>
            <input type="text" class="form-control" name="search" placeholder="Search..." value="{{ request.GET.search }}">
        </div>
        {% endif %}
        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
            <button type="submit" class="btn btn-primary w-100 mt-2 mt-md-0">
                <i class="fas fa-filter me-2"></i>Apply Filter
            </button>
        </div>
    </form>
</div>

<!-- Class-Section filter functionality is loaded from the global JS file -->
<script src="{% static 'dist/js/class-section-filter.js' %}"></script>