{% extends 'super_admin/base.html' %}
{% load i18n %}

{% block title %}Delete College - {{ college.name }} | Gurukul Setu{% endblock %}

{% block page_title %}Delete College{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i> Confirm Deletion
                </div>
                <div class="card-body text-center py-5">
                    <i class="fas fa-trash-alt fa-4x text-danger mb-4"></i>
                    <h3>Are you sure you want to delete this college?</h3>
                    <h5 class="text-muted mb-4">{{ college.name }} ({{ college.code }})</h5>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. All data associated with this college will be permanently deleted.
                    </div>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash-alt me-2"></i> Delete College
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
