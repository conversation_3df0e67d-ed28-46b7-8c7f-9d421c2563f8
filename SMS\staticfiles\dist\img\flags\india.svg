<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="900" height="600" viewBox="0 0 900 600">
  <rect width="900" height="600" fill="#f8f9fa"/>
  <rect width="900" height="200" fill="#FF9933"/>
  <rect width="900" height="200" fill="#FFFFFF" y="200"/>
  <rect width="900" height="200" fill="#138808" y="400"/>
  <circle cx="450" cy="300" r="60" fill="#000080"/>
  <circle cx="450" cy="300" r="55" fill="#FFFFFF"/>
  <circle cx="450" cy="300" r="20" fill="#000080"/>
  <g id="spokes">
    <line x1="450" y1="300" x2="450" y2="245" stroke="#000080" stroke-width="3.5"/>
    <line x1="450" y1="300" x2="450" y2="355" stroke="#000080" stroke-width="3.5"/>
    <line x1="450" y1="300" x2="505" y2="300" stroke="#000080" stroke-width="3.5"/>
    <line x1="450" y1="300" x2="395" y2="300" stroke="#000080" stroke-width="3.5"/>
  </g>
  <use href="#spokes" transform="rotate(22.5, 450, 300)"/>
  <use href="#spokes" transform="rotate(45, 450, 300)"/>
  <use href="#spokes" transform="rotate(67.5, 450, 300)"/>
</svg>
