# Generated by Django 5.1.3 on 2025-05-26 13:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fees', '0005_feepayment_transaction_id'),
        ('super_admin', '0004_userprofile_bio_userprofile_designation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='feepayment',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fee_payments', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='pendingfee',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pending_fees', to='super_admin.college'),
        ),
    ]
