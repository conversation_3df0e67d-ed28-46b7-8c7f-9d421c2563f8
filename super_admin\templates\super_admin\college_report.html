{% extends 'super_admin/base.html' %}
{% load i18n %}

{% block title %}College Reports | Gurukul Setu{% endblock %}

{% block page_title %}College Reports{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        transition: all 0.3s;
    }
    
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="card report-card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-university fa-3x mb-3 text-primary"></i>
                    <h5 class="card-title">Total Colleges</h5>
                    <h2 class="mb-0">{{ colleges.count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <h5 class="card-title">Active Colleges</h5>
                    <h2 class="mb-0">{{ active_colleges }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-3x mb-3 text-danger"></i>
                    <h5 class="card-title">Inactive Colleges</h5>
                    <h2 class="mb-0">{{ inactive_colleges }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card report-card mb-4">
                <div class="card-body text-center">
                    <i class="fas fa-map-marker-alt fa-3x mb-3 text-warning"></i>
                    <h5 class="card-title">States Covered</h5>
                    <h2 class="mb-0">{{ colleges_by_state.count }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Colleges by State -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-map-marked-alt me-2"></i> Colleges by State
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="stateChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active vs Inactive -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-2"></i> Active vs Inactive Colleges
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- College List -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-list me-2"></i> College List
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="collegeTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for college in colleges %}
                            <tr>
                                <td>{{ college.name }}</td>
                                <td>{{ college.code }}</td>
                                <td>{{ college.city }}, {{ college.state }}</td>
                                <td>
                                    {% if college.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ college.created_at|date:"d M Y" }}</td>
                                <td>
                                    <a href="{% url 'super_admin:college_detail' college.pk %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Colleges by State Chart
        const stateCtx = document.getElementById('stateChart').getContext('2d');
        const stateData = {
            labels: [
                {% for item in colleges_by_state %}
                    '{{ item.state }}',
                {% endfor %}
            ],
            datasets: [{
                label: 'Number of Colleges',
                data: [
                    {% for item in colleges_by_state %}
                        {{ item.count }},
                    {% endfor %}
                ],
                backgroundColor: [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#5a5c69', '#858796', '#6f42c1', '#20c9a6', '#f8f9fc'
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(stateCtx, {
            type: 'bar',
            data: stateData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Active vs Inactive Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusData = {
            labels: ['Active', 'Inactive'],
            datasets: [{
                data: [{{ active_colleges }}, {{ inactive_colleges }}],
                backgroundColor: ['#1cc88a', '#e74a3b'],
                hoverBackgroundColor: ['#17a673', '#d52a1a'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }]
        };
        
        new Chart(statusCtx, {
            type: 'doughnut',
            data: statusData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Initialize DataTable
        $(document).ready(function() {
            $('#collegeTable').DataTable({
                order: [[4, 'desc']]
            });
        });
    });
</script>
{% endblock %}
