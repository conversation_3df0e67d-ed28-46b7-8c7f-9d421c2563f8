{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load widget_tweaks %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        Edit College - {{ form.instance.name }}
    {% else %}
        Add New College
    {% endif %}
    | Gurukul Setu
{% endblock %}

{% block page_title %}
    {% if form.instance.pk %}
        Edit College - {{ form.instance.name }}
    {% else %}
        Add New College
    {% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/college-management.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas {% if form.instance.pk %}fa-edit{% else %}fa-plus{% endif %} me-2"></i>
                    {% if form.instance.pk %}
                        Edit College Details
                    {% else %}
                        Add New College
                    {% endif %}
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Please correct the following errors:</strong>
                                <ul>
                                    {% for field in form %}
                                        {% for error in field.errors %}
                                            <li>{{ field.label }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>Basic Information</h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }} *</label>
                                    {{ form.name|add_class:"form-control" }}
                                    {% if form.name.help_text %}
                                        <small class="form-text text-muted">{{ form.name.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }} *</label>
                                    {{ form.code|add_class:"form-control" }}
                                    {% if form.code.help_text %}
                                        <small class="form-text text-muted">{{ form.code.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label">{{ form.logo.label }}</label>
                                    {{ form.logo|add_class:"form-control" }}
                                    {% if form.logo.help_text %}
                                        <small class="form-text text-muted">{{ form.logo.help_text }}</small>
                                    {% endif %}
                                    {% if form.instance.logo %}
                                        <div class="mt-2">
                                            <img src="{{ form.instance.logo.url }}" alt="Current Logo" class="img-thumbnail" style="max-height: 100px;">
                                            <p class="small">Current logo</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                    {{ form.description|add_class:"form-control"|attr:"rows:5" }}
                                    {% if form.description.help_text %}
                                        <small class="form-text text-muted">{{ form.description.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.website.id_for_label }}" class="form-label">{{ form.website.label }}</label>
                                    {{ form.website|add_class:"form-control" }}
                                    {% if form.website.help_text %}
                                        <small class="form-text text-muted">{{ form.website.help_text }}</small>
                                    {% endif %}
                                </div>

                                {% if form.instance.pk %}
                                    <div class="form-group mb-3">
                                        <div class="form-check form-switch">
                                            {{ form.is_active|add_class:"form-check-input" }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                {{ form.is_active.label }}
                                            </label>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>Contact Information</h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }} *</label>
                                    {{ form.address|add_class:"form-control"|attr:"rows:3" }}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.city.id_for_label }}" class="form-label">{{ form.city.label }} *</label>
                                            {{ form.city|add_class:"form-control" }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="{{ form.state.id_for_label }}" class="form-label">{{ form.state.label }} *</label>
                                            {{ form.state|add_class:"form-control" }}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.pincode.id_for_label }}" class="form-label">{{ form.pincode.label }} *</label>
                                    {{ form.pincode|add_class:"form-control" }}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }} *</label>
                                    {{ form.email|add_class:"form-control" }}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }} *</label>
                                    {{ form.phone|add_class:"form-control" }}
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <h5>Subscription Information</h5>
                                <hr>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="{{ form.subscription_plan.id_for_label }}" class="form-label">{{ form.subscription_plan.label }}</label>
                                    {{ form.subscription_plan|add_class:"form-control" }}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="{{ form.subscription_start_date.id_for_label }}" class="form-label">{{ form.subscription_start_date.label }}</label>
                                    {{ form.subscription_start_date|add_class:"form-control"|attr:"type:date" }}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="{{ form.subscription_end_date.id_for_label }}" class="form-label">{{ form.subscription_end_date.label }}</label>
                                    {{ form.subscription_end_date|add_class:"form-control"|attr:"type:date" }}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'super_admin:college_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if form.instance.pk %}
                                            Update College
                                        {% else %}
                                            Save College
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add date picker for date fields
    document.addEventListener('DOMContentLoaded', function() {
        // Add any custom form validation or enhancements here
    });
</script>
{% endblock %}
