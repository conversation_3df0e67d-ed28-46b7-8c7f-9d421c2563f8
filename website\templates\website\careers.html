{% extends 'website/base.html' %}
{% load static %}

{% block title %}Careers - Gurukul Setu{% endblock %}

{% block content %}
<!-- Page Banner -->
<section class="page-banner">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="page-banner-content">
                    <h1>Join Our Team</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'landing_page' %}">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Careers</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Careers Intro Section -->
<section class="careers-intro-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="careers-intro-content">
                    <div class="section-heading">
                        <h6>Work With Us</h6>
                        <h2>Build the Future of Education Technology</h2>
                        <p>At Gurukul Setu, we're on a mission to transform education management in India. Join our team of passionate professionals who are dedicated to making a difference in the education sector.</p>
                    </div>
                    <div class="careers-intro-features">
                        <div class="feature-item">
                            <div class="icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="content">
                                <h4>Innovation-Driven</h4>
                                <p>Work on cutting-edge technology solutions that solve real problems in education.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="content">
                                <h4>Collaborative Culture</h4>
                                <p>Be part of a diverse team that values collaboration, creativity, and continuous learning.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="content">
                                <h4>Growth Opportunities</h4>
                                <p>Develop your skills and advance your career in a rapidly growing company.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="content">
                                <h4>Meaningful Impact</h4>
                                <p>Make a real difference in the education of thousands of students across India.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="careers-intro-image">
                    <img src="{% static 'img/careers-intro.jpg' %}" alt="Gurukul Setu Team" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Values Section -->
<section class="our-values-section">
    <div class="container">
        <div class="section-header text-center">
            <h6>Our Core Values</h6>
            <h2>What Drives Us Every Day</h2>
            <p>These values form the foundation of our company culture and guide everything we do.</p>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="value-card">
                    <div class="icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>Excellence</h3>
                    <p>We strive for excellence in everything we do, from product development to customer service.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="value-card">
                    <div class="icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3>Innovation</h3>
                    <p>We embrace innovation and continuously seek new ways to improve our products and services.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="value-card">
                    <div class="icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3>Integrity</h3>
                    <p>We act with honesty, transparency, and ethical responsibility in all our interactions.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="value-card">
                    <div class="icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <h3>Customer-Centricity</h3>
                    <p>We put our customers at the center of everything we do and strive to exceed their expectations.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="benefits-section">
    <div class="container">
        <div class="section-header text-center">
            <h6>Employee Benefits</h6>
            <h2>Why Work at Gurukul Setu</h2>
            <p>We offer a comprehensive benefits package to support our team members' well-being and professional growth.</p>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-hand-holding-medical"></i>
                    </div>
                    <h3>Health & Wellness</h3>
                    <ul>
                        <li>Comprehensive health insurance</li>
                        <li>Mental health support</li>
                        <li>Wellness programs</li>
                        <li>Gym membership subsidy</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Learning & Development</h3>
                    <ul>
                        <li>Professional development budget</li>
                        <li>Regular training workshops</li>
                        <li>Conference attendance</li>
                        <li>Mentorship programs</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-balance-scale-right"></i>
                    </div>
                    <h3>Work-Life Balance</h3>
                    <ul>
                        <li>Flexible working hours</li>
                        <li>Remote work options</li>
                        <li>Generous paid time off</li>
                        <li>Parental leave</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3>Financial Benefits</h3>
                    <ul>
                        <li>Competitive salary</li>
                        <li>Performance bonuses</li>
                        <li>Employee stock options</li>
                        <li>Retirement plans</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-laptop-house"></i>
                    </div>
                    <h3>Work Environment</h3>
                    <ul>
                        <li>Modern office space</li>
                        <li>Latest technology and tools</li>
                        <li>Casual dress code</li>
                        <li>Team building activities</li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="benefit-card">
                    <div class="icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <h3>Additional Perks</h3>
                    <ul>
                        <li>Free snacks and beverages</li>
                        <li>Team lunches</li>
                        <li>Transportation allowance</li>
                        <li>Festival celebrations</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Open Positions Section -->
<section class="open-positions-section">
    <div class="container">
        <div class="section-header text-center">
            <h6>Join Our Team</h6>
            <h2>Current Openings</h2>
            <p>We're looking for talented individuals to join our growing team. Check out our current openings below.</p>
        </div>
        
        <div class="position-filters">
            <div class="row">
                <div class="col-lg-4">
                    <select class="form-select" id="departmentFilter">
                        <option value="all">All Departments</option>
                        <option value="engineering">Engineering</option>
                        <option value="product">Product</option>
                        <option value="design">Design</option>
                        <option value="marketing">Marketing</option>
                        <option value="sales">Sales</option>
                        <option value="customer-success">Customer Success</option>
                    </select>
                </div>
                <div class="col-lg-4">
                    <select class="form-select" id="locationFilter">
                        <option value="all">All Locations</option>
                        <option value="bangalore">Bangalore</option>
                        <option value="delhi">Delhi</option>
                        <option value="remote">Remote</option>
                    </select>
                </div>
                <div class="col-lg-4">
                    <select class="form-select" id="experienceFilter">
                        <option value="all">All Experience Levels</option>
                        <option value="entry">Entry Level (0-2 years)</option>
                        <option value="mid">Mid Level (3-5 years)</option>
                        <option value="senior">Senior Level (6+ years)</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="position-list">
            <!-- Engineering Positions -->
            <div class="position-card" data-department="engineering" data-location="bangalore" data-experience="mid">
                <div class="position-header">
                    <h3>Senior Backend Developer</h3>
                    <div class="position-meta">
                        <span><i class="fas fa-map-marker-alt"></i> Bangalore</span>
                        <span><i class="fas fa-briefcase"></i> Full-time</span>
                        <span><i class="fas fa-layer-group"></i> Engineering</span>
                    </div>
                </div>
                <div class="position-body">
                    <p>We're looking for an experienced Backend Developer to help build and scale our core platform. You'll work with a talented team to develop robust, scalable, and maintainable backend services.</p>
                    <div class="position-requirements">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>3+ years of experience in backend development</li>
                            <li>Proficiency in Python and Django</li>
                            <li>Experience with RESTful APIs and microservices</li>
                            <li>Knowledge of database design and optimization</li>
                            <li>Understanding of cloud services (AWS/Azure/GCP)</li>
                        </ul>
                    </div>
                </div>
                <div class="position-footer">
                    <a href="#" class="btn btn-outline" data-bs-toggle="modal" data-bs-target="#applyModal" data-position="Senior Backend Developer">Apply Now</a>
                </div>
            </div>
            
            <div class="position-card" data-department="engineering" data-location="remote" data-experience="entry">
                <div class="position-header">
                    <h3>Frontend Developer</h3>
                    <div class="position-meta">
                        <span><i class="fas fa-map-marker-alt"></i> Remote</span>
                        <span><i class="fas fa-briefcase"></i> Full-time</span>
                        <span><i class="fas fa-layer-group"></i> Engineering</span>
                    </div>
                </div>
                <div class="position-body">
                    <p>We're seeking a talented Frontend Developer to create engaging and intuitive user interfaces for our web applications. You'll collaborate with designers and backend developers to implement responsive and accessible web pages.</p>
                    <div class="position-requirements">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>1-2 years of experience in frontend development</li>
                            <li>Proficiency in HTML, CSS, and JavaScript</li>
                            <li>Experience with React or Vue.js</li>
                            <li>Understanding of responsive design principles</li>
                            <li>Knowledge of modern frontend build tools</li>
                        </ul>
                    </div>
                </div>
                <div class="position-footer">
                    <a href="#" class="btn btn-outline" data-bs-toggle="modal" data-bs-target="#applyModal" data-position="Frontend Developer">Apply Now</a>
                </div>
            </div>
            
            <!-- Product Positions -->
            <div class="position-card" data-department="product" data-location="bangalore" data-experience="senior">
                <div class="position-header">
                    <h3>Product Manager</h3>
                    <div class="position-meta">
                        <span><i class="fas fa-map-marker-alt"></i> Bangalore</span>
                        <span><i class="fas fa-briefcase"></i> Full-time</span>
                        <span><i class="fas fa-layer-group"></i> Product</span>
                    </div>
                </div>
                <div class="position-body">
                    <p>We're looking for an experienced Product Manager to lead the development and growth of our education management platform. You'll work closely with engineering, design, and business teams to define product strategy and roadmap.</p>
                    <div class="position-requirements">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>6+ years of experience in product management</li>
                            <li>Experience with SaaS products, preferably in the education sector</li>
                            <li>Strong analytical and problem-solving skills</li>
                            <li>Excellent communication and stakeholder management abilities</li>
                            <li>Data-driven approach to decision making</li>
                        </ul>
                    </div>
                </div>
                <div class="position-footer">
                    <a href="#" class="btn btn-outline" data-bs-toggle="modal" data-bs-target="#applyModal" data-position="Product Manager">Apply Now</a>
                </div>
            </div>
            
            <!-- Marketing Positions -->
            <div class="position-card" data-department="marketing" data-location="delhi" data-experience="mid">
                <div class="position-header">
                    <h3>Digital Marketing Specialist</h3>
                    <div class="position-meta">
                        <span><i class="fas fa-map-marker-alt"></i> Delhi</span>
                        <span><i class="fas fa-briefcase"></i> Full-time</span>
                        <span><i class="fas fa-layer-group"></i> Marketing</span>
                    </div>
                </div>
                <div class="position-body">
                    <p>We're seeking a Digital Marketing Specialist to drive our online marketing efforts. You'll be responsible for planning and executing digital marketing campaigns to increase brand awareness and generate leads.</p>
                    <div class="position-requirements">
                        <h4>Requirements:</h4>
                        <ul>
                            <li>3-5 years of experience in digital marketing</li>
                            <li>Proficiency in SEO, SEM, and social media marketing</li>
                            <li>Experience with marketing automation tools</li>
                            <li>Strong analytical skills and familiarity with marketing metrics</li>
                            <li>Excellent content creation and copywriting abilities</li>
                        </ul>
                    </div>
                </div>
                <div class="position-footer">
                    <a href="#" class="btn btn-outline" data-bs-toggle="modal" data-bs-target="#applyModal" data-position="Digital Marketing Specialist">Apply Now</a>
                </div>
            </div>
        </div>
        
        <div class="no-positions-message" style="display: none;">
            <p>No positions match your current filters. Please try different filter options or check back later for new openings.</p>
        </div>
        
        <div class="text-center mt-5">
            <p>Don't see a position that matches your skills? Send us your resume anyway!</p>
            <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#applyModal" data-position="General Application">General Application</a>
        </div>
    </div>
</section>

<!-- Application Modal -->
<div class="modal fade" id="applyModal" tabindex="-1" aria-labelledby="applyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="applyModalLabel">Apply for <span id="positionTitle"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="applicationForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="firstName">First Name*</label>
                                <input type="text" class="form-control" id="firstName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="lastName">Last Name*</label>
                                <input type="text" class="form-control" id="lastName" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group mb-3">
                        <label for="email">Email Address*</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="phone">Phone Number*</label>
                        <input type="tel" class="form-control" id="phone" required>
                    </div>
                    <div class="form-group mb-3">
                        <label for="resume">Resume/CV*</label>
                        <input type="file" class="form-control" id="resume" required>
                        <small class="form-text text-muted">Accepted formats: PDF, DOC, DOCX (Max size: 5MB)</small>
                    </div>
                    <div class="form-group mb-3">
                        <label for="coverLetter">Cover Letter</label>
                        <textarea class="form-control" id="coverLetter" rows="4"></textarea>
                    </div>
                    <div class="form-group mb-3">
                        <label for="portfolio">Portfolio/GitHub/LinkedIn URL</label>
                        <input type="url" class="form-control" id="portfolio">
                    </div>
                    <div class="form-group mb-3">
                        <label>How did you hear about us?</label>
                        <select class="form-select" id="source">
                            <option value="">Please select</option>
                            <option value="Job Board">Job Board</option>
                            <option value="Company Website">Company Website</option>
                            <option value="Social Media">Social Media</option>
                            <option value="Referral">Referral</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-check mb-4">
                        <input type="checkbox" class="form-check-input" id="privacyConsent" required>
                        <label class="form-check-label" for="privacyConsent">I consent to Gurukul Setu processing my personal data for recruitment purposes.*</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="submitApplication">Submit Application</button>
            </div>
        </div>
    </div>
</div>

<!-- Team Culture Section -->
<section class="team-culture-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="team-culture-image">
                    <img src="{% static 'img/team-culture.jpg' %}" alt="Team Culture" class="img-fluid">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="team-culture-content">
                    <div class="section-heading">
                        <h6>Our Culture</h6>
                        <h2>Life at Gurukul Setu</h2>
                        <p>We believe that a positive work environment leads to innovation, productivity, and job satisfaction. Here's what you can expect when you join our team:</p>
                    </div>
                    <div class="culture-points">
                        <div class="culture-point">
                            <h4><i class="fas fa-check-circle"></i> Collaborative Environment</h4>
                            <p>We foster a culture of collaboration where ideas are freely shared and everyone's input is valued.</p>
                        </div>
                        <div class="culture-point">
                            <h4><i class="fas fa-check-circle"></i> Continuous Learning</h4>
                            <p>We encourage continuous learning and provide resources for professional development.</p>
                        </div>
                        <div class="culture-point">
                            <h4><i class="fas fa-check-circle"></i> Work-Life Balance</h4>
                            <p>We respect the importance of work-life balance and offer flexible working arrangements.</p>
                        </div>
                        <div class="culture-point">
                            <h4><i class="fas fa-check-circle"></i> Diversity and Inclusion</h4>
                            <p>We celebrate diversity and are committed to creating an inclusive workplace where everyone feels welcome.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="cta-content">
                    <h2>Ready to Join Our Team?</h2>
                    <p>Explore our current openings and take the first step towards a rewarding career at Gurukul Setu.</p>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="cta-btn text-lg-end">
                    <a href="#" class="btn btn-light" onclick="document.querySelector('.open-positions-section').scrollIntoView({behavior: 'smooth'}); return false;">View Open Positions</a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Position filtering
        const departmentFilter = document.getElementById('departmentFilter');
        const locationFilter = document.getElementById('locationFilter');
        const experienceFilter = document.getElementById('experienceFilter');
        const positionCards = document.querySelectorAll('.position-card');
        const noPositionsMessage = document.querySelector('.no-positions-message');
        
        function filterPositions() {
            const department = departmentFilter.value;
            const location = locationFilter.value;
            const experience = experienceFilter.value;
            
            let visibleCount = 0;
            
            positionCards.forEach(card => {
                const cardDepartment = card.getAttribute('data-department');
                const cardLocation = card.getAttribute('data-location');
                const cardExperience = card.getAttribute('data-experience');
                
                const departmentMatch = department === 'all' || cardDepartment === department;
                const locationMatch = location === 'all' || cardLocation === location;
                const experienceMatch = experience === 'all' || cardExperience === experience;
                
                if (departmentMatch && locationMatch && experienceMatch) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show/hide no positions message
            if (visibleCount === 0) {
                noPositionsMessage.style.display = 'block';
            } else {
                noPositionsMessage.style.display = 'none';
            }
        }
        
        // Add event listeners to filters
        departmentFilter.addEventListener('change', filterPositions);
        locationFilter.addEventListener('change', filterPositions);
        experienceFilter.addEventListener('change', filterPositions);
        
        // Application modal
        const applyModal = document.getElementById('applyModal');
        const positionTitle = document.getElementById('positionTitle');
        const submitApplication = document.getElementById('submitApplication');
        const applicationForm = document.getElementById('applicationForm');
        
        if (applyModal) {
            applyModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const position = button.getAttribute('data-position');
                positionTitle.textContent = position;
            });
        }
        
        if (submitApplication) {
            submitApplication.addEventListener('click', function() {
                // Check form validity
                const formElements = applicationForm.elements;
                let isValid = true;
                
                for (let i = 0; i < formElements.length; i++) {
                    if (formElements[i].hasAttribute('required') && !formElements[i].value) {
                        isValid = false;
                        formElements[i].classList.add('is-invalid');
                    } else {
                        formElements[i].classList.remove('is-invalid');
                    }
                }
                
                if (isValid) {
                    // Here you would typically send the form data to a server
                    // For now, we'll just show a success message
                    alert('Thank you for your application! Our HR team will review it and contact you soon.');
                    applicationForm.reset();
                    
                    // Close the modal
                    const modal = bootstrap.Modal.getInstance(applyModal);
                    modal.hide();
                } else {
                    alert('Please fill in all required fields.');
                }
            });
        }
    });
</script>
{% endblock %}
