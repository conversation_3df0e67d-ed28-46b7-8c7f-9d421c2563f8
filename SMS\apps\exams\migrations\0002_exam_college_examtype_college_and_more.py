# Generated by Django 5.1.3 on 2025-05-26 13:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('exams', '0001_initial'),
        ('super_admin', '0004_userprofile_bio_userprofile_designation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='exam',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='exams', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='examtype',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='exam_types', to='super_admin.college'),
        ),
        migrations.AlterUniqueTogether(
            name='examtype',
            unique_together={('name', 'college')},
        ),
    ]
