/* Language Selector Styles */
.language-selector {
  position: relative;
  display: inline-block;
  margin-left: 15px;
}

.language-selector-toggle {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.85rem;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.language-selector-toggle:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.language-selector-toggle i {
  margin-right: 5px;
  font-size: 14px;
}

.language-selector-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  min-width: 150px;
  z-index: 1000;
  display: none;
  overflow: hidden;
  margin-top: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.language-selector-dropdown.show {
  display: block;
  animation: fadeIn 0.2s ease;
}

.language-option {
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.language-option:last-child {
  border-bottom: none;
}

.language-option:hover {
  background-color: #f5f5f5;
}

.language-option.active {
  background-color: #f0f6fc;
  font-weight: 500;
}

.language-flag {
  width: 20px;
  height: 15px;
  margin-right: 10px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Language indicator in content */
.language-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  background-color: rgba(0, 0, 0, 0.05);
  margin-left: 8px;
  color: #666;
}

.language-indicator i {
  font-size: 10px;
  margin-right: 4px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .language-selector {
    margin-left: 5px;
  }
  
  .language-selector-toggle {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
  
  .language-selector-toggle span {
    display: none;
  }
  
  .language-selector-toggle i {
    margin-right: 0;
  }
}
