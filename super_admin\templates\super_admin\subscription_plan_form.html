{% extends 'super_admin/base.html' %}
{% load i18n %}
{% load widget_tweaks %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        Edit Subscription Plan
    {% else %}
        Add New Subscription Plan
    {% endif %}
    | Gurukul Setu
{% endblock %}

{% block page_title %}
    {% if form.instance.pk %}
        Edit Subscription Plan
    {% else %}
        Add New Subscription Plan
    {% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/subscription-management.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas {% if form.instance.pk %}fa-edit{% else %}fa-plus{% endif %} me-2"></i>
                    {% if form.instance.pk %}
                        Edit Subscription Plan
                    {% else %}
                        Add New Subscription Plan
                    {% endif %}
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.errors %}
                            <div class="alert alert-danger">
                                <strong>Please correct the following errors:</strong>
                                <ul>
                                    {% for field in form %}
                                        {% for error in field.errors %}
                                            <li>{{ field.label }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }} *</label>
                                    {{ form.name|add_class:"form-control" }}
                                    {% if form.name.help_text %}
                                        <small class="form-text text-muted">{{ form.name.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.duration_months.id_for_label }}" class="form-label">{{ form.duration_months.label }} *</label>
                                    {{ form.duration_months|add_class:"form-control" }}
                                    {% if form.duration_months.help_text %}
                                        <small class="form-text text-muted">{{ form.duration_months.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.price.id_for_label }}" class="form-label">{{ form.price.label }} *</label>
                                    {{ form.price|add_class:"form-control" }}
                                    {% if form.price.help_text %}
                                        <small class="form-text text-muted">{{ form.price.help_text }}</small>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                                    {{ form.description|add_class:"form-control" }}
                                    {% if form.description.help_text %}
                                        <small class="form-text text-muted">{{ form.description.help_text }}</small>
                                    {% endif %}
                                </div>

                                <div class="form-group mb-3">
                                    <label for="{{ form.features.id_for_label }}" class="form-label">{{ form.features.label }}</label>
                                    {{ form.features|add_class:"form-control" }}
                                    <small class="form-text text-muted">Enter one feature per line</small>
                                </div>

                                <div class="form-check form-switch mb-3">
                                    {{ form.is_active|add_class:"form-check-input" }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">{{ form.is_active.label }}</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'super_admin:subscription_plan_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if form.instance.pk %}
                                            Update Plan
                                        {% else %}
                                            Save Plan
                                        {% endif %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
