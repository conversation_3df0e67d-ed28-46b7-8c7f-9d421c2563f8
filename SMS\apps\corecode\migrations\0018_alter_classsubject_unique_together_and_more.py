# Generated by Django 5.1.3 on 2025-05-26 13:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('corecode', '0017_automatedbackupsettings_backup'),
        ('super_admin', '0004_userprofile_bio_userprofile_designation_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='classsubject',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='classteacher',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='feesettings',
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name='section',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='academicsession',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='academic_sessions', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='academicterm',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='academic_terms', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='classsubject',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='class_subjects', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='classteacher',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='class_teachers', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='feesettings',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fee_settings', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='feestructure',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fee_structures', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='section',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='studentclass',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_classes', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='subject',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='super_admin.college'),
        ),
        migrations.AlterField(
            model_name='academicsession',
            name='name',
            field=models.CharField(max_length=200),
        ),
        migrations.AlterField(
            model_name='academicterm',
            name='name',
            field=models.CharField(max_length=20),
        ),
        migrations.AlterField(
            model_name='studentclass',
            name='name',
            field=models.CharField(max_length=200),
        ),
        migrations.AlterField(
            model_name='subject',
            name='name',
            field=models.CharField(max_length=200),
        ),
        migrations.AlterUniqueTogether(
            name='academicsession',
            unique_together={('name', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='academicterm',
            unique_together={('name', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='classsubject',
            unique_together={('subject', 'student_class', 'section', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='classteacher',
            unique_together={('student_class', 'section', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='feesettings',
            unique_together={('class_name', 'section', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='section',
            unique_together={('student_class', 'name', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='studentclass',
            unique_together={('name', 'college')},
        ),
        migrations.AlterUniqueTogether(
            name='subject',
            unique_together={('name', 'college')},
        ),
    ]
