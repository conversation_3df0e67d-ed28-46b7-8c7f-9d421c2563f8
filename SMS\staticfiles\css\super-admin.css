/* Super Admin CSS Styles */
:root {
    --primary-color: #309898;
    --secondary-color: #2a8686;
    --light-color: #e9f7f7;
    --dark-color: #1a5555;
    --white-color: #ffffff;
    --gray-color: #f8f9fa;
    --text-color: #333333;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 250px;
    background-color: var(--primary-color);
    color: var(--white-color);
    z-index: 1000;
    transition: all 0.3s;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header img {
    max-width: 50px;
    margin-bottom: 10px;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: block;
    padding: 10px 20px;
    color: var(--white-color);
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: var(--dark-color);
    border-left: 4px solid var(--white-color);
}

.sidebar-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Main content */
.main-content {
    margin-left: 250px;
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s;
}

/* Topbar */
.topbar {
    background-color: var(--white-color);
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.topbar h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: var(--primary-color);
    color: var(--white-color);
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    color: var(--primary-color);
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
}

/* Report Cards */
.report-card {
    transition: all 0.3s;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
        padding: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar.active {
        width: 250px;
    }
}
