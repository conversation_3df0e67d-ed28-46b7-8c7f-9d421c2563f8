# Generated by Django 5.1.3 on 2025-05-26 13:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('attendance', '0012_delete_staffattendance'),
        ('super_admin', '0004_userprofile_bio_userprofile_designation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='attendance',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='super_admin.college'),
        ),
        migrations.AddField(
            model_name='holiday',
            name='college',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='holidays', to='super_admin.college'),
        ),
        migrations.AlterField(
            model_name='holiday',
            name='date',
            field=models.DateField(),
        ),
        migrations.AlterUniqueTogether(
            name='holiday',
            unique_together={('date', 'college')},
        ),
    ]
