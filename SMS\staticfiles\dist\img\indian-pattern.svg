<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <style>
    .pattern-bg { fill: #F4F1EA; }
    .pattern-element { fill: none; stroke: #FF9933; stroke-width: 1; opacity: 0.3; }
    .pattern-element-2 { fill: none; stroke: #138808; stroke-width: 1; opacity: 0.3; }
    .pattern-element-3 { fill: none; stroke: #000080; stroke-width: 1; opacity: 0.3; }
    .chakra { fill: #000080; opacity: 0.1; }
  </style>
  
  <!-- Background -->
  <rect class="pattern-bg" x="0" y="0" width="200" height="200"/>
  
  <!-- <PERSON> Pattern Elements -->
  <path class="pattern-element" d="M20,20 C30,10 40,15 35,25 C30,35 15,30 20,20 Z"/>
  <path class="pattern-element" d="M60,20 C70,10 80,15 75,25 C70,35 55,30 60,20 Z"/>
  <path class="pattern-element" d="M100,20 C110,10 120,15 115,25 C110,35 95,30 100,20 Z"/>
  <path class="pattern-element" d="M140,20 C150,10 160,15 155,25 C150,35 135,30 140,20 Z"/>
  <path class="pattern-element" d="M180,20 C190,10 200,15 195,25 C190,35 175,30 180,20 Z"/>
  
  <path class="pattern-element" d="M20,60 C30,50 40,55 35,65 C30,75 15,70 20,60 Z"/>
  <path class="pattern-element" d="M60,60 C70,50 80,55 75,65 C70,75 55,70 60,60 Z"/>
  <path class="pattern-element" d="M100,60 C110,50 120,55 115,65 C110,75 95,70 100,60 Z"/>
  <path class="pattern-element" d="M140,60 C150,50 160,55 155,65 C150,75 135,70 140,60 Z"/>
  <path class="pattern-element" d="M180,60 C190,50 200,55 195,65 C190,75 175,70 180,60 Z"/>
  
  <path class="pattern-element" d="M20,100 C30,90 40,95 35,105 C30,115 15,110 20,100 Z"/>
  <path class="pattern-element" d="M60,100 C70,90 80,95 75,105 C70,115 55,110 60,100 Z"/>
  <path class="pattern-element" d="M100,100 C110,90 120,95 115,105 C110,115 95,110 100,100 Z"/>
  <path class="pattern-element" d="M140,100 C150,90 160,95 155,105 C150,115 135,110 140,100 Z"/>
  <path class="pattern-element" d="M180,100 C190,90 200,95 195,105 C190,115 175,110 180,100 Z"/>
  
  <path class="pattern-element" d="M20,140 C30,130 40,135 35,145 C30,155 15,150 20,140 Z"/>
  <path class="pattern-element" d="M60,140 C70,130 80,135 75,145 C70,155 55,150 60,140 Z"/>
  <path class="pattern-element" d="M100,140 C110,130 120,135 115,145 C110,155 95,150 100,140 Z"/>
  <path class="pattern-element" d="M140,140 C150,130 160,135 155,145 C150,155 135,150 140,140 Z"/>
  <path class="pattern-element" d="M180,140 C190,130 200,135 195,145 C190,155 175,150 180,140 Z"/>
  
  <path class="pattern-element" d="M20,180 C30,170 40,175 35,185 C30,195 15,190 20,180 Z"/>
  <path class="pattern-element" d="M60,180 C70,170 80,175 75,185 C70,195 55,190 60,180 Z"/>
  <path class="pattern-element" d="M100,180 C110,170 120,175 115,185 C110,195 95,190 100,180 Z"/>
  <path class="pattern-element" d="M140,180 C150,170 160,175 155,185 C150,195 135,190 140,180 Z"/>
  <path class="pattern-element" d="M180,180 C190,170 200,175 195,185 C190,195 175,190 180,180 Z"/>
  
  <!-- Rangoli Pattern Elements -->
  <path class="pattern-element-2" d="M40,40 L50,30 L60,40 L50,50 Z"/>
  <path class="pattern-element-2" d="M80,40 L90,30 L100,40 L90,50 Z"/>
  <path class="pattern-element-2" d="M120,40 L130,30 L140,40 L130,50 Z"/>
  <path class="pattern-element-2" d="M160,40 L170,30 L180,40 L170,50 Z"/>
  
  <path class="pattern-element-2" d="M40,80 L50,70 L60,80 L50,90 Z"/>
  <path class="pattern-element-2" d="M80,80 L90,70 L100,80 L90,90 Z"/>
  <path class="pattern-element-2" d="M120,80 L130,70 L140,80 L130,90 Z"/>
  <path class="pattern-element-2" d="M160,80 L170,70 L180,80 L170,90 Z"/>
  
  <path class="pattern-element-2" d="M40,120 L50,110 L60,120 L50,130 Z"/>
  <path class="pattern-element-2" d="M80,120 L90,110 L100,120 L90,130 Z"/>
  <path class="pattern-element-2" d="M120,120 L130,110 L140,120 L130,130 Z"/>
  <path class="pattern-element-2" d="M160,120 L170,110 L180,120 L170,130 Z"/>
  
  <path class="pattern-element-2" d="M40,160 L50,150 L60,160 L50,170 Z"/>
  <path class="pattern-element-2" d="M80,160 L90,150 L100,160 L90,170 Z"/>
  <path class="pattern-element-2" d="M120,160 L130,150 L140,160 L130,170 Z"/>
  <path class="pattern-element-2" d="M160,160 L170,150 L180,160 L170,170 Z"/>
  
  <!-- Chakra Elements -->
  <circle class="chakra" cx="40" cy="40" r="5"/>
  <circle class="chakra" cx="120" cy="40" r="5"/>
  <circle class="chakra" cx="80" cy="80" r="5"/>
  <circle class="chakra" cx="160" cy="80" r="5"/>
  <circle class="chakra" cx="40" cy="120" r="5"/>
  <circle class="chakra" cx="120" cy="120" r="5"/>
  <circle class="chakra" cx="80" cy="160" r="5"/>
  <circle class="chakra" cx="160" cy="160" r="5"/>
  
  <!-- Connecting Lines -->
  <path class="pattern-element-3" d="M0,0 L200,200"/>
  <path class="pattern-element-3" d="M200,0 L0,200"/>
  <path class="pattern-element-3" d="M100,0 L100,200"/>
  <path class="pattern-element-3" d="M0,100 L200,100"/>
</svg>
